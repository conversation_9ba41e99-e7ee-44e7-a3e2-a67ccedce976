import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  VStack,
  HStack,
  Grid,
  Grid<PERSON>tem,
  Card,
  <PERSON>ing,
  Separator,
  Badge,
  Accordion,
} from "@chakra-ui/react"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, useNavigate, useParams } from "@tanstack/react-router"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
// Simple arrow left icon component
const ArrowLeftIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

// A4 Size Box Component
// A4 dimensions: 210mm × 297mm ≈ 794px × 1123px at 96 DPI
const A4Box = ({ children, ...props }: { children: React.ReactNode } & any) => (
  <Box
    width="794px"
    minHeight="1123px"
    maxWidth="100%"
    mx="auto"
    bg="white"
    border="1px solid #e0e0e0"
    borderRadius="8px"
    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
    p={6}
    position="relative"
    {...props}
  >
    {children}
  </Box>
)

import { QuestionnairesService } from "@/client"
import Header from "@/components/header"
import { ChineseText } from "@/components/ui/fonts"

export const Route = createFileRoute("/_layout/questionnaire-details/$questionnaireId")({
  component: QuestionnaireDetailsPage,
})

// Helper function to get survey type display name
const getSurveyTypeDisplayName = (surveyTypeName: string | null) => {
  if (!surveyTypeName) return "未知評估類型"
  
  switch (surveyTypeName) {
    case "3-6 years questionnaire":
      return "3-6歲"
    case "7-11 years questionnaire":
      return "7-11歲"
    case "12-15 years questionnaire":
      return "12-15歲"
    default:
      return surveyTypeName
  }
}

// Helper function to format date
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "yyyy年MM月dd日 HH:mm", { locale: zhCN })
  } catch {
    return "未知日期"
  }
}

// Helper function to get answer option labels
const getAnswerLabel = (answer: string) => {
  const labels: Record<string, string> = {
    A: "實際情況相比題目描述表現得更出色（或表現出超高的天份）",
    B: "與實際情況比較，小朋友的情況完全符合題目的描述",
    C: "與實際情況比較，小朋友的情況大部份符合題目的描述",
    D: "與實際情況比較，小朋友的情況小部份符合題目的描述",
    E: "與實際情況比較，小朋友的情況基本不份符合題目的描述",
    F: "與實際情況比較，小朋友的情況完全不份符合題目的描述",
  }
  return labels[answer] || `選項 ${answer}`
}

// Scoring calculation functions
const calculateAnswerScore = (selectedAnswer: string, totalQuestionsInCompetency: number): number => {
  // Maximum score per question = 15 / total_questions_in_competency
  const maxScorePerQuestion = 15.0 / totalQuestionsInCompetency

  // Answer scoring scale: A=100%, B=80%, C=60%, D=40%, E=20%, F=0%
  const answerPercentages: Record<string, number> = {
    'A': 1.0,   // 100%
    'B': 0.8,   // 80%
    'C': 0.6,   // 60%
    'D': 0.4,   // 40%
    'E': 0.2,   // 20%
    'F': 0.0    // 0%
  }

  const percentage = answerPercentages[selectedAnswer.toUpperCase()] || 0.0
  return maxScorePerQuestion * percentage
}

const calculateCoreCompetencyScores = (answers: any[]): Record<string, number> => {
  // Group answers by core competency
  const competencyAnswers: Record<string, any[]> = {}

  for (const answer of answers) {
    const competencyName = answer.core_competency_name || ''
    if (competencyName) {
      if (!competencyAnswers[competencyName]) {
        competencyAnswers[competencyName] = []
      }
      competencyAnswers[competencyName].push(answer)
    }
  }

  // Calculate scores for each core competency
  const competencyScores: Record<string, number> = {}

  for (const [competencyName, competencyAnswerList] of Object.entries(competencyAnswers)) {
    const totalQuestions = competencyAnswerList.length
    let totalScore = 0.0

    for (const answer of competencyAnswerList) {
      const selectedAnswer = answer.selected_answer || 'F'
      const score = calculateAnswerScore(selectedAnswer, totalQuestions)
      totalScore += score
    }

    competencyScores[competencyName] = Math.round(totalScore * 10) / 10
  }

  return competencyScores
}

const calculateIntelligenceCategoryScores = (
  answers: any[],
  competencyScores: Record<string, number>
): Record<string, { totalScore: number; competencies: Record<string, number> }> => {
  // Group competencies by intelligence category
  const categoryCompetencies: Record<string, Set<string>> = {}

  for (const answer of answers) {
    const categoryName = answer.intelligence_category_name || ''
    const competencyName = answer.core_competency_name || ''
    if (categoryName && competencyName) {
      if (!categoryCompetencies[categoryName]) {
        categoryCompetencies[categoryName] = new Set()
      }
      categoryCompetencies[categoryName].add(competencyName)
    }
  }

  // Calculate category scores
  const categoryScores: Record<string, { totalScore: number; competencies: Record<string, number> }> = {}

  for (const [categoryName, competencies] of Object.entries(categoryCompetencies)) {
    let categoryTotal = 0.0
    const categoryCompetencyScores: Record<string, number> = {}

    for (const competencyName of competencies) {
      const competencyScore = competencyScores[competencyName] || 0.0
      categoryCompetencyScores[competencyName] = competencyScore
      categoryTotal += competencyScore
    }

    categoryScores[categoryName] = {
      totalScore: Math.round(categoryTotal * 10) / 10,
      competencies: categoryCompetencyScores
    }
  }

  return categoryScores
}

function QuestionnaireDetailsPage() {
  const navigate = useNavigate()
  const { questionnaireId } = useParams({ strict: false })

  // Get questionnaire details
  const { data: questionnaire, isLoading, error } = useQuery({
    queryKey: ["questionnaire-details", questionnaireId],
    queryFn: () => QuestionnairesService.getMyQuestionnaireDetails({
      questionnaireId: questionnaireId || ""
    }),
    enabled: !!questionnaireId,
  })

  if (isLoading) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter">載入問卷詳情中...</Text>
        </VStack>
      </Box>
    )
  }

  if (error || !questionnaire) {
    return (
      <Box bg="#f9f5e9" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <VStack gap={4}>
          <Text fontSize="24px" fontFamily="Inter" color="red.500">問卷不存在或載入失敗</Text>
          <Button onClick={() => navigate({ to: "/questionnaire-history" })}>返回問卷記錄</Button>
        </VStack>
      </Box>
    )
  }

  // Extract answers from questionnaire data
  const answers = (questionnaire.questions_and_ans?.answers || []) as any[]

  // Group answers by intelligence category
  const answersByCategory = answers.reduce((acc: Record<string, any[]>, answer: any) => {
    const category = answer.intelligence_category_name || "其他"
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(answer)
    return acc
  }, {} as Record<string, any[]>)

  // Calculate scores
  const competencyScores = calculateCoreCompetencyScores(answers)
  const categoryScores = calculateIntelligenceCategoryScores(answers, competencyScores)

  // Calculate total score
  const totalScore = Object.values(categoryScores).reduce((sum, category) => sum + category.totalScore, 0)

  return (
    <Box bg="#f9f5e9" minH="100vh">
      <Header title="問卷詳情" />
      
      <Box maxW="1200px" mx="auto" px={8} py={8}>
        <VStack gap={8} align="stretch">
          {/* Back Button */}
          <HStack>
            <Button
              variant="ghost"
              onClick={() => navigate({ to: "/questionnaire-history" })}
              color="#666666"
              _hover={{ bg: "rgba(0,0,0,0.05)" }}
            >
              <ArrowLeftIcon />
              <ChineseText fontFamily="Inter" ml={2}>返回問卷記錄</ChineseText>
            </Button>
          </HStack>

          {/* Basic Information Card */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                基本資訊
              </Heading>
            </Card.Header>
            <Card.Body>
              <Grid templateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap={4}>
                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        孩子姓名：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {questionnaire.name}
                      </ChineseText>
                    </HStack>
                    
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        評估類型：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {getSurveyTypeDisplayName(questionnaire.survey_type_name)}智能核心評估
                      </ChineseText>
                    </HStack>

                    {questionnaire.birth_info && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          出生資訊：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.birth_info}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.grade_level && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          年級：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.grade_level}
                        </ChineseText>
                      </HStack>
                    )}
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    {questionnaire.caregiver && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          照顧者：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.caregiver}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.feeding && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          餵養方式：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.feeding}
                        </ChineseText>
                      </HStack>
                    )}

                    {questionnaire.native_language && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          母語：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {questionnaire.native_language}
                        </ChineseText>
                      </HStack>
                    )}

                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        狀態：
                      </ChineseText>
                      <Badge colorScheme="green" fontSize="12px">
                        已完成
                      </Badge>
                    </HStack>
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="flex-start" gap={2}>
                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        開始時間：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {formatDate(questionnaire.created_at)}
                      </ChineseText>
                    </HStack>

                    {questionnaire.finished_at && (
                      <HStack>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                          完成時間：
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                          {formatDate(questionnaire.finished_at)}
                        </ChineseText>
                      </HStack>
                    )}

                    <HStack>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                        總題數：
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                        {answers.length} 題
                      </ChineseText>
                    </HStack>
                  </VStack>
                </GridItem>
              </Grid>
            </Card.Body>
          </Card.Root>

          {/* Questions and Answers Section */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                問題與答案
              </Heading>
            </Card.Header>
            <Card.Body>
              {Object.keys(answersByCategory).length === 0 ? (
                <ChineseText fontSize="16px" color="#666666" fontFamily="Inter" textAlign="center">
                  暫無答案記錄
                </ChineseText>
              ) : (
                <Accordion.Root multiple>
                  {Object.entries(answersByCategory).map(([category, categoryAnswers]) => (
                    <Accordion.Item key={category} value={category} border="1px solid #e0e0e0" borderRadius="8px" mb={4}>
                      <Accordion.ItemTrigger _hover={{ bg: "rgba(0,0,0,0.02)" }}>
                        <Box flex="1" textAlign="left">
                          <ChineseText fontSize="18px" fontWeight="bold" color="#000000" fontFamily="Inter">
                            {category} ({categoryAnswers.length} 題)
                          </ChineseText>
                        </Box>
                        <Accordion.ItemIndicator />
                      </Accordion.ItemTrigger>
                      <Accordion.ItemContent>
                        <Accordion.ItemBody pb={4}>
                        <VStack gap={4} align="stretch">
                          {categoryAnswers.map((answer: any, index: number) => (
                            <Box key={index} p={4} border="1px solid #f0f0f0" borderRadius="8px" bg="#fafafa">
                              <VStack align="flex-start" gap={3}>
                                <ChineseText fontSize="16px" fontWeight="bold" color="#000000" fontFamily="Inter">
                                  問題 {answer.question_id}
                                </ChineseText>
                                
                                <ChineseText fontSize="14px" color="#333333" fontFamily="Inter" lineHeight="1.6">
                                  {answer.question_text}
                                </ChineseText>

                                <Separator />

                                <HStack align="flex-start" gap={2}>
                                  <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                                    核心能力：
                                  </ChineseText>
                                  <ChineseText fontSize="14px" color="#000000" fontFamily="Inter">
                                    {answer.core_competency_name}
                                  </ChineseText>
                                </HStack>

                                <HStack align="flex-start" gap={2}>
                                  <ChineseText fontSize="14px" color="#666666" fontFamily="Inter" fontWeight="bold">
                                    選擇答案：
                                  </ChineseText>
                                  <Badge colorScheme="blue" fontSize="12px">
                                    {answer.selected_answer}
                                  </Badge>
                                </HStack>

                                <Box pl={4} borderLeft="3px solid #d3401f">
                                  <ChineseText fontSize="14px" color="#333333" fontFamily="Inter" lineHeight="1.5">
                                    {getAnswerLabel(answer.selected_answer)}
                                  </ChineseText>
                                </Box>
                              </VStack>
                            </Box>
                          ))}
                        </VStack>
                        </Accordion.ItemBody>
                      </Accordion.ItemContent>
                    </Accordion.Item>
                  ))}
                </Accordion.Root>
              )}
            </Card.Body>
          </Card.Root>

          {/* A4 Report Section */}
          <Card.Root border="1px solid #e0e0e0" borderRadius="12px">
            <Card.Header>
              <Heading size="md" color="#000000" fontFamily="Inter">
                評估報告
              </Heading>
            </Card.Header>
            <Card.Body>
              <A4Box>
                <VStack gap={6} align="stretch">
                  {/* Report Header */}
                  <Box textAlign="center" borderBottom="2px solid #d3401f" pb={4}>
                    <ChineseText fontSize="28px" fontWeight="bold" color="#d3401f" fontFamily="Inter" mb={2}>
                      智能核心評估報告
                    </ChineseText>
                    <ChineseText fontSize="18px" color="#666666" fontFamily="Inter">
                      {questionnaire.name} - {getSurveyTypeDisplayName(questionnaire.survey_type_name)}
                    </ChineseText>
                    <ChineseText fontSize="14px" color="#888888" fontFamily="Inter" mt={2}>
                      完成時間：{questionnaire.finished_at ? formatDate(questionnaire.finished_at) : "未知"}
                    </ChineseText>
                  </Box>

                  {/* Overall Score Summary */}
                  <Box bg="#f8f9fa" p={4} borderRadius="8px" border="1px solid #e0e0e0">
                    <HStack justify="space-between" align="center">
                      <ChineseText fontSize="20px" fontWeight="bold" color="#000000" fontFamily="Inter">
                        總體評估分數
                      </ChineseText>
                      <Box textAlign="right">
                        <ChineseText fontSize="32px" fontWeight="bold" color="#d3401f" fontFamily="Inter">
                          {Math.round(totalScore * 10) / 10}
                        </ChineseText>
                        <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                          滿分：{Object.keys(categoryScores).length * 60} 分
                        </ChineseText>
                      </Box>
                    </HStack>
                  </Box>

                  {/* Detailed Scoring by Intelligence Categories */}
                  <Box>
                    <ChineseText fontSize="22px" fontWeight="bold" color="#000000" fontFamily="Inter" mb={4}>
                      詳細評分報告
                    </ChineseText>

                    <Grid templateColumns="repeat(2, 1fr)" gap={6}>
                      {Object.entries(categoryScores)
                        .sort(([a], [b]) => a.localeCompare(b))
                        .map(([categoryName, categoryData]) => (
                        <GridItem key={categoryName}>
                          <Box border="1px solid #e0e0e0" borderRadius="8px" p={4} bg="white">
                            {/* Intelligence Category Header */}
                            <HStack justify="space-between" align="center" mb={3} pb={2} borderBottom="1px solid #f0f0f0">
                              <ChineseText fontSize="18px" fontWeight="bold" color="#d3401f" fontFamily="Inter">
                                {categoryName}
                              </ChineseText>
                              <ChineseText fontSize="20px" fontWeight="bold" color="#d3401f" fontFamily="Inter">
                                {categoryData.totalScore}
                              </ChineseText>
                            </HStack>

                            {/* Core Competencies */}
                            <VStack align="stretch" gap={2}>
                              {Object.entries(categoryData.competencies)
                                .sort(([a], [b]) => a.localeCompare(b))
                                .map(([competencyName, competencyScore]) => (
                                <HStack key={competencyName} justify="space-between" pl={4}>
                                  <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                                    - {competencyName}
                                  </ChineseText>
                                  <ChineseText fontSize="14px" fontWeight="bold" color="#333333" fontFamily="Inter">
                                    {competencyScore}
                                  </ChineseText>
                                </HStack>
                              ))}
                            </VStack>
                          </Box>
                        </GridItem>
                      ))}
                    </Grid>
                  </Box>

                  {/* Scoring Legend */}
                  <Box bg="#f8f9fa" p={4} borderRadius="8px" border="1px solid #e0e0e0">
                    <ChineseText fontSize="16px" fontWeight="bold" color="#000000" fontFamily="Inter" mb={3}>
                      評分說明
                    </ChineseText>
                    <VStack align="stretch" gap={2}>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                        • 每個核心能力滿分為 15 分
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                        • 智能類別分數為其包含的所有核心能力分數總和
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                        • 答案選項評分：A=滿分，B=80%，C=60%，D=40%，E=20%，F=0%
                      </ChineseText>
                      <ChineseText fontSize="14px" color="#666666" fontFamily="Inter">
                        • 總分為所有智能類別分數的總和
                      </ChineseText>
                    </VStack>
                  </Box>

                  {/* Report Footer */}
                  <Box textAlign="center" pt={4} borderTop="1px solid #e0e0e0">
                    <ChineseText fontSize="12px" color="#888888" fontFamily="Inter">
                      此報告由智能核心評估系統自動生成 | 生成時間：{new Date().toLocaleString('zh-TW')}
                    </ChineseText>
                  </Box>
                </VStack>
              </A4Box>
            </Card.Body>
          </Card.Root>

          {/* Action Buttons */}
          <HStack justify="center" gap={4} pt={4}>
            <Button
              bg="#d3401f"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => window.print()}
              _hover={{ bg: "#b8351a" }}
            >
              📄 列印詳情
            </Button>
            <Button
              bg="#2196F3"
              color="#ffffff"
              borderRadius="8px"
              h="48px"
              px="32px"
              fontSize="18px"
              fontWeight="600"
              fontFamily="Inter"
              onClick={() => navigate({ to: "/questionnaire-history" })}
              _hover={{ bg: "#1976D2" }}
            >
              返回記錄
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Box>
  )
}
